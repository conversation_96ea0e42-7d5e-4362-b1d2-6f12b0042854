require('dotenv').config();
require('aws-sdk/lib/maintenance_mode_message').suppress = true;
const { schedule } = require("node-cron");
const { optimizeCurrentMonthCoords } = require('./jobs/optimizeCurrentMonthCoords');
const { generateDailyStatistics, generateWeeklyStatistics } = require('./services/statsProcessor');
const { generateDailySummaryReports, generateWeeklySummaryReports, generateMonthlySummaryReports } = require('./services/summaryReportsProcessor');
const { computeVesselOnlineLookup } = require('./services/vesselOnlineProcessor');
const { backupMongoDB } = require('./services/dbBackup');
const { createLoggerWithPath } = require('./modules/winston');

console.log('process has started')

require('./modules/processLogs')
const db = require('./modules/db');
const { postLogToSlack } = require('./modules/notifyLog');

Promise.all(
    Object.values(db).map(db => new Promise((resolve, reject) => {
        db.once('open', resolve);
        db.on('error', reject);
    }))
).then(() => {
    const logger = createLoggerWithPath('index');
    logger.info('Starting index.js');
    require('./services/iotProcessor')
    require('./services/statsProcessor')
    require('./services/notificationAlertsProcessor')
    require('./services/newArtifactProcessor')
    require('./services/summaryReportsProcessor')
    require('./services/vesselOnlineProcessor')
    require('./services/seaVisionSampleRequests')
    require('./services/audioProcessor')

    // Schedule all cron jobs
    logger.info('Scheduling cron jobs');
    schedule('1 0 12 * * *', function () { generateDailySummaryReports({}) }, { scheduled: true, timezone: 'UTC' }); // at 12:00:01 each day
    schedule('1 0 16 * * Sunday', function () { generateWeeklySummaryReports({}) }, { scheduled: true, timezone: 'UTC' }); // at 16:00:01 each Sunday
    schedule('1 0 0 1 * *', function () { generateMonthlySummaryReports({}) }, { scheduled: true, timezone: 'UTC' }); // at 00:00:01 on the 1st day
    schedule('0 */5 * * * *', computeVesselOnlineLookup, { scheduled: true, timezone: 'UTC' }); // every 5 minutes
    schedule('1 0 12 * * *', generateDailyStatistics, { scheduled: true, timezone: 'UTC' }); // at 12:00:01 each day
    schedule('1 0 16 * * Sunday', generateWeeklyStatistics, { scheduled: true, timezone: 'UTC' }); // at 16:00:01 each Sunday
    schedule('0 59 23 * * *', optimizeCurrentMonthCoords, { scheduled: true, timezone: 'UTC' }); // at 23:59:00 each day
    schedule('0 1 * * *', () => {
        console.log('DB backup cron job initialized');
        backupMongoDB();
    }, { scheduled: true, timezone: 'UTC' }); // at 01:00:00 each day
})

process.on('uncaughtException', (err) => {
    console.error('(FATAL ERROR) Uncaught Exception:', err)
    postLogToSlack({
        severity: 'fatal',
        message: 'Uncaught Exception in the microservices process',
        stack: err.stack
    })
})

// test node-js ci