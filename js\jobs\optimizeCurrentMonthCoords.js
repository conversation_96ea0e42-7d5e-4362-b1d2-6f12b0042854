const db = require("../modules/db");
const simplify = require('simplify-js');
const { postLogToSlack } = require("../modules/notifyLog");

const getSimplifiedCoords = (coords) => {

    // Step 1: Prepare points with a reference index
    const points = coords.map((c, i) => ({
        x: c.location.coordinates[0],
        y: c.location.coordinates[1],
        _index: i // store original index
    }));

    // Step 2: Simplify
    const simplified = simplify(points, 0.0001, true);

    // Step 3: Map back to original objects
    const result = simplified.map(p => coords[p._index]);

    return result;
}

const getMonthRange = (month) => {
    const [year, monthNum] = month.split('-').map(Number);
    const startDate = new Date(Date.UTC(year, monthNum - 1, 1, 0, 0, 0, 0));
    const endDate = new Date(Date.UTC(year, monthNum, 0, 23, 59, 59, 999));
    return [startDate, endDate];
}

async function optimizeCurrentMonthCoords() {
    try {
        const ts = new Date().getTime()

        const month = new Date().toISOString().split('T')[0].split('-').slice(0, 2).join('-')
        const dateRange = getMonthRange(month)
        console.log('[optimizeCurrentMonthCoords] month', month, 'dateRange', dateRange)

        if (!(await db.locationsOptimized.listCollections({ name: month })).find(c => c.name === month)) {
            throw new Error(`Collection ${month} does not exist in DB ${db.locationsOptimized.name}`)
        }

        if (!(await db.locationsRaw.listCollections({ name: month })).find(c => c.name === month)) {
            throw new Error(`Collection ${month} does not exist in DB ${db.locationsRaw.name}`)
        }

        console.log('[optimizeCurrentMonthCoords] fetching coordinates from DB', db.locationsRaw.name)
        let allCoords = (await db.locationsRaw.collection(month).find({
            timestamp: {
                $gte: dateRange[0],
                $lte: dateRange[1]
            }
        }, {
            projection: {
                details: 0
            }
        }).toArray()).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

        console.log('[optimizeCurrentMonthCoords] allCoords', allCoords.length)

        const ditinctVesselIds = [...new Set(allCoords.map(c => c.metadata.onboardVesselId?.toString()))].filter(vesselId => vesselId)
        console.log('[optimizeCurrentMonthCoords] ditinctVesselIds', ditinctVesselIds.length)

        let optCoords = []
        ditinctVesselIds.forEach(vesselId => {
            const coords = allCoords.filter(c => c.metadata.onboardVesselId?.toString() === vesselId).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
            const optimizedCoords = getSimplifiedCoords(coords)
            optCoords.push(...optimizedCoords)
        })

        console.log('[optimizeCurrentMonthCoords] optCoords', optCoords.length, 'vs', allCoords.length)

        // clean memory
        const allCoordsLength = allCoords.length
        allCoords = null
        console.log('[optimizeCurrentMonthCoords] cleaned memory')

        const optimizedCollection = db.locationsOptimized.collection(month)
        await optimizedCollection.deleteMany({})
        await optimizedCollection.insertMany(optCoords)

        console.log('[optimizeCurrentMonthCoords] Success')
        console.log('[optimizeCurrentMonthCoords] Time taken:', new Date().getTime() - ts, 'ms')

        postLogToSlack({
            severity: 'info',
            message: `Monthly coordinate optimization completed successfully for ${month}\nTime taken: ${new Date().getTime() - ts}ms\nOptimized: ${optCoords.length} from ${allCoordsLength} coordinates`,
            stack: 'N/A'
        });
    } catch (error) {
        console.error('Error optimizing current month coords:', error);
        postLogToSlack({
            severity: 'fatal',
            message: 'Fatal error while optimizing current month coords',
            stack: error.stack
        })
    }
}

module.exports = { optimizeCurrentMonthCoords };