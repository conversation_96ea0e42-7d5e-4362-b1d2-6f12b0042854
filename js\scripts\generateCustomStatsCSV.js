// node scripts/generateAllTimeStatsCSV.js --output-dir=path/to/dir

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const db = require('../modules/db');
const Vessel = require('../models/Vessel');
const RegionGroup = require('../models/RegionGroup');
const HomePort = require('../models/HomePort');
const { getSessionsByCoordinates, getLocationsCollections } = require('../utils/functions');
const ExcelJS = require('exceljs');
const workbook = new ExcelJS.Workbook();
const mongoose = require('mongoose');

const REGION_GROUP_ID = new mongoose.Types.ObjectId('681c253f9f43051a7748b2c1');
const START_DATE = new Date('2025-11-17');
const END_DATE = new Date('2025-11-30');

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

async function generateAllTimeStatsCSV() {
    try {
        const EXCLUDED_UNIT_IDS = ['QSX0004'];

        writeLine(`🔍 Building vessel mapping in environment ${process.env.NODE_ENV}...`);
        const vessels = await Vessel.find({ region_group_id: REGION_GROUP_ID, unit_id: { $nin: EXCLUDED_UNIT_IDS } }, { unit_id: 1, onboard_vessel_id: 1, _id: 1, name: 1, region_group_id: 1 });
        const vesselIds = vessels.map(vessel => vessel._id);
        const vesselNames = {};
        const vesselUnitIds = {};
        const vesselRegionGroupIds = {};
        vessels.forEach(vessel => {
            vesselNames[vessel._id.toString()] = vessel.name || vessel.onboard_vessel_id.toString();
            vesselUnitIds[vessel._id.toString()] = vessel.unit_id || '';
            vesselRegionGroupIds[vessel._id.toString()] = vessel.region_group_id;
        });
        writeLine(`✅ Found ${Object.keys(vesselNames).length} vessels${EXCLUDED_UNIT_IDS.length > 0 ? ` (excluding ${EXCLUDED_UNIT_IDS.join(', ')})` : ''}.`);

        // Fetch region groups to resolve region_group_id to names
        writeLine('🔍 Building region group mapping...');
        const regionGroups = await RegionGroup.find({ _id: REGION_GROUP_ID }, { _id: 1, name: 1 });
        const regionGroupNames = {};
        regionGroups.forEach(rg => {
            regionGroupNames[rg._id.toString()] = rg.name || 'Unknown';
        });
        writeLine(`✅ Found ${regionGroups.length} region groups.`);

        // Prepare stats containers
        const totalSmartmastsDistanceTraveled = {};
        const totalSensorsOnlineDuration = {};
        const totalVesselsDetectedbySensors = {};
        const totalVesselsSuperCategorized = {};
        const totalVesselsSubCategorized = {};
        // Add per-vessel category counters
        const perVesselSuperCategorized = {};
        const perVesselSubCategorized = {};

        // Process artifacts using a cursor (streaming, with projection)
        writeLine('🔍 Processing artifacts...');
        const artifactsCursor = db.qmai.db.collection('analysis_results').find(
            { vessel_presence: true, super_category: { $ne: null }, onboard_vessel_id: { $in: vesselIds }, timestamp: { $gte: START_DATE, $lte: END_DATE } },
            { projection: { unit_id: 1, onboard_vessel_id: 1, super_category: 1, category: 1, det_nbbox_area: 1, vessel_presence: 1, timestamp: 1 } }
        );
        let artifactCount = 0;
        let earliestDate = null;
        let latestDate = null;
        for await (const a of artifactsCursor) {
            artifactCount++;

            // Track date range
            if (a.timestamp) {
                const artifactDate = new Date(a.timestamp);
                if (!earliestDate || artifactDate < earliestDate) {
                    earliestDate = artifactDate;
                }
                if (!latestDate || artifactDate > latestDate) {
                    latestDate = artifactDate;
                }
            }

            if (!a.onboard_vessel_id) continue;
            let vesselId = a.onboard_vessel_id.toString();
            if (!vesselId) continue;
            if (!totalVesselsDetectedbySensors[vesselId]) totalVesselsDetectedbySensors[vesselId] = 0;
            totalVesselsDetectedbySensors[vesselId] += 1;
            if (a.det_nbbox_area >= 0.03) {
                if (!totalVesselsSuperCategorized[a.super_category]) totalVesselsSuperCategorized[a.super_category] = 0;
                totalVesselsSuperCategorized[a.super_category] += 1;
                if (!perVesselSuperCategorized[vesselId]) perVesselSuperCategorized[vesselId] = {};
                if (!perVesselSuperCategorized[vesselId][a.super_category]) perVesselSuperCategorized[vesselId][a.super_category] = 0;
                perVesselSuperCategorized[vesselId][a.super_category] += 1;
            }
            if (a.category && a.det_nbbox_area >= 0.03) {
                if (!totalVesselsSubCategorized[a.category]) totalVesselsSubCategorized[a.category] = 0;
                totalVesselsSubCategorized[a.category] += 1;
                if (!perVesselSubCategorized[vesselId]) perVesselSubCategorized[vesselId] = {};
                if (!perVesselSubCategorized[vesselId][a.category]) perVesselSubCategorized[vesselId][a.category] = 0;
                perVesselSubCategorized[vesselId][a.category] += 1;
            }
        }
        writeLine(`✅ Processed ${artifactCount} artifacts.`);

        // Process locations collection by collection, streaming, with projection
        writeLine('🔍 Processing location collections...');
        const collections = await getLocationsCollections(db.locationsRaw, START_DATE, END_DATE);
        writeLine(`Found ${collections.length} location collections. ${collections.map(collection => collection.name).join(', ')}`);
        const LatLonSpherical = (await import('geodesy/latlon-spherical.js')).default;
        // Fetch home ports with projection
        // const homePorts = await HomePort.find({}, { lat: 1, lng: 1, _id: 0 });
        const homePorts = vessels.filter(vessel => vessel.home_port_location && vessel.home_port_location.coordinates).map(vessel => {
            return {
                lat: vessel.home_port_location.coordinates[1],
                lng: vessel.home_port_location.coordinates[0],
            }
        });
        const stationaryDistance = 15; // meters
        let locCollectionIdx = 0;
        let totalLocations = 0;
        for (const collection of collections) {
            locCollectionIdx++;
            const collectionName = collection.name;
            writeProgress(`Processing collection ${locCollectionIdx}/${collections.length}: ${collectionName} ...`);
            const cursor = collection.find(
                { "metadata.onboardVesselId": { $in: vesselIds }, timestamp: { $gte: START_DATE, $lte: END_DATE } },
                {
                    projection: {
                        metadata: 1,
                        latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                        longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                        timestamp: 1
                    },
                    sort: { timestamp: 1 }
                }
            );
            // Group locations by onboardVesselId in memory
            const vesselLocsMap = {};
            let locCount = 0;
            for await (const loc of cursor) {
                if (!loc.metadata.onboardVesselId) continue;
                const vesselId = loc.metadata.onboardVesselId.toString();
                if (!vesselLocsMap[vesselId]) vesselLocsMap[vesselId] = [];
                vesselLocsMap[vesselId].push(loc);
                locCount++;
            }
            totalLocations += locCount;
            writeLine(`✅ Processed collection ${locCollectionIdx}/${collections.length}: ${collectionName} (${locCount} locations)`);
            for (const vesselId of Object.keys(vesselLocsMap)) {
                const locations = vesselLocsMap[vesselId];
                const sessions = getSessionsByCoordinates(locations);
                let onlineDuration = 0;
                if (sessions.length !== 0) {
                    onlineDuration = sessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
                }
                totalSensorsOnlineDuration[vesselId] = (totalSensorsOnlineDuration[vesselId] || 0) + onlineDuration;

                // At-sea duration (filter out home ports)
                const seaLocations = locations.filter(loc => {
                    if (homePorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
                        return false;
                    else
                        return true;
                });
                const seaSessions = getSessionsByCoordinates(seaLocations);
                let atSeaDuration = 0;
                if (seaSessions.length !== 0) {
                    atSeaDuration = seaSessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
                }
                // (If you want to output at-sea duration, you can add it to the Excel as well)

                // Distance travelled (excluding stationary, and only at sea)
                let distanceSum = 0;
                if (seaLocations.length >= 2) {
                    var totalDistance = 0;
                    seaLocations.forEach((loc, i) => {
                        const nextLoc = seaLocations[i + 1];
                        if (nextLoc) {
                            const distance = new LatLonSpherical(loc.latitude, loc.longitude).distanceTo(new LatLonSpherical(nextLoc.latitude, nextLoc.longitude));
                            if (distance >= stationaryDistance) totalDistance += distance;
                        }
                    });
                    distanceSum = totalDistance;
                }
                totalSmartmastsDistanceTraveled[vesselId] = (totalSmartmastsDistanceTraveled[vesselId] || 0) + distanceSum;
            }
        }
        writeLine(`✅ All location collections processed. Total locations: ${totalLocations}`);

        // Prepare two-row CSV header
        const mainHeaders = [
            'Vessel ID',
            'Sensor ID',
            'Vessel Name',
            'Region Group',
            'Miles Travelled',
            'Hours Online',
            'Vessels Detected',
        ];
        const superCategories = Object.keys(totalVesselsSuperCategorized);
        const subCategories = Object.keys(totalVesselsSubCategorized);
        // First header row: main columns, then 'Super Category' (spanning), then 'Category' (spanning)
        const headerRow1 = [
            ...mainHeaders,
            ...Array(superCategories.length).fill('Super Category'),
            ...Array(subCategories.length).fill('Category')
        ];
        // Second header row: empty for main columns, then actual super-category and category names
        const headerRow2 = [
            ...Array(mainHeaders.length).fill(''),
            ...superCategories,
            ...subCategories
        ];

        // Prepare CSV rows (per vessel) - sort by region group name alphabetically
        const allVesselIds = Object.keys(vesselNames).sort((a, b) => {
            const regionA = vesselRegionGroupIds[a] ? (regionGroupNames[vesselRegionGroupIds[a].toString()] || vesselRegionGroupIds[a].toString()) : 'Unknown';
            const regionB = vesselRegionGroupIds[b] ? (regionGroupNames[vesselRegionGroupIds[b].toString()] || vesselRegionGroupIds[b].toString()) : 'Unknown';
            return regionA.localeCompare(regionB);
        });
        const rows = [headerRow1, headerRow2];
        for (const vesselId of allVesselIds) {
            const name = vesselNames[vesselId] || '';
            const unitId = vesselUnitIds[vesselId] || '';
            const regionGroupId = vesselRegionGroupIds[vesselId];
            const regionGroupName = regionGroupId ? (regionGroupNames[regionGroupId.toString()] || regionGroupId.toString()) : 'Unknown';
            const milesTravelled = ((totalSmartmastsDistanceTraveled[vesselId] || 0) / 1609.34).toFixed(2); // meters to miles
            const hoursOnline = ((totalSensorsOnlineDuration[vesselId] || 0) / (1000 * 60 * 60)).toFixed(2); // ms to hours
            const vesselsDetected = totalVesselsDetectedbySensors[vesselId] || 0;
            const row = [
                vesselId,
                unitId,
                name,
                regionGroupName,
                milesTravelled,
                hoursOnline,
                vesselsDetected,
            ];
            // Add super-category counts (per vessel)
            row.push(...superCategories.map(c => (perVesselSuperCategorized[vesselId] && perVesselSuperCategorized[vesselId][c] || 0)));
            // Add sub-category counts (per vessel)
            row.push(...subCategories.map(c => (perVesselSubCategorized[vesselId] && perVesselSubCategorized[vesselId][c] || 0)));
            rows.push(row);
        }

        // Calculate summary row (sum of all vessels)
        const summaryRow = [
            'ALL',
            'ALL SENSORS',
            'ALL VESSELS',
            'ALL REGIONS',
        ];
        // Sum numeric columns
        let sumMiles = 0,
            sumHours = 0,
            sumDetected = 0;
        let sumSuperCats = superCategories.map(() => 0);
        let sumSubCats = subCategories.map(() => 0);
        for (const vesselId of allVesselIds) {
            sumMiles += Number(((totalSmartmastsDistanceTraveled[vesselId] || 0) / 1609.34));
            sumHours += Number(((totalSensorsOnlineDuration[vesselId] || 0) / (1000 * 60 * 60)));
            sumDetected += totalVesselsDetectedbySensors[vesselId] || 0;
        }
        for (let i = 0; i < superCategories.length; ++i) {
            sumSuperCats[i] = totalVesselsSuperCategorized[superCategories[i]] || 0;
        }
        for (let i = 0; i < subCategories.length; ++i) {
            sumSubCats[i] = totalVesselsSubCategorized[subCategories[i]] || 0;
        }
        summaryRow.push(sumMiles.toFixed(2));
        summaryRow.push(sumHours.toFixed(2));
        summaryRow.push(sumDetected);
        summaryRow.push(...sumSuperCats);
        summaryRow.push(...sumSubCats);

        // Add a blank line and a header for the summary
        rows.push([]);
        rows.push(['Summary (Sum of All Vessels)']);
        rows.push(headerRow1);
        rows.push(headerRow2);
        rows.push(summaryRow);

        // --- Excel file generation ---
        writeLine('🔍 Generating Excel file...');
        const worksheet = workbook.addWorksheet('All Time Stats');

        // Add date range header at the top
        const formatDate = (date) => {
            if (!date) return 'Unknown';
            const year = date.getUTCFullYear();
            const month = String(date.getUTCMonth() + 1).padStart(2, '0');
            const day = String(date.getUTCDate()).padStart(2, '0');
            const hours = String(date.getUTCHours()).padStart(2, '0');
            const minutes = String(date.getUTCMinutes()).padStart(2, '0');
            const seconds = String(date.getUTCSeconds()).padStart(2, '0');
            const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds} UTC`;
        };

        const dateRangeText = `Data Period: ${formatDate(earliestDate)} to ${formatDate(latestDate)}`;
        const dateRangeRow = worksheet.addRow([dateRangeText]);
        dateRangeRow.font = { bold: true, size: 14 };
        dateRangeRow.alignment = { horizontal: 'center' };

        worksheet.mergeCells(1, 1, 1, 3);

        // Add empty row for spacing
        worksheet.addRow([]);

        // Write the two header rows for per-vessel section
        worksheet.addRow(headerRow1);
        worksheet.addRow(headerRow2);

        // Write per-vessel rows and accumulate totals for each column
        const perVesselRows = [];
        let totalMiles = 0,
            totalHours = 0,
            totalDetected = 0;
        let totalSuperCats = superCategories.map(() => 0);
        let totalSubCats = subCategories.map(() => 0);

        // Region group totals
        const regionGroupTotals = {};
        const regionGroupSuperCats = {};
        const regionGroupSubCats = {};

        // Sort vessels by region group name for Excel generation too
        const sortedVesselIds = Object.keys(vesselNames).sort((a, b) => {
            const regionA = vesselRegionGroupIds[a] ? (regionGroupNames[vesselRegionGroupIds[a].toString()] || vesselRegionGroupIds[a].toString()) : 'Unknown';
            const regionB = vesselRegionGroupIds[b] ? (regionGroupNames[vesselRegionGroupIds[b].toString()] || vesselRegionGroupIds[b].toString()) : 'Unknown';
            return regionA.localeCompare(regionB);
        });

        for (const vesselId of sortedVesselIds) {
            const name = vesselNames[vesselId] || '';
            const unitId = vesselUnitIds[vesselId] || '';
            const regionGroupId = vesselRegionGroupIds[vesselId];
            const regionGroupName = regionGroupId ? (regionGroupNames[regionGroupId.toString()] || regionGroupId.toString()) : 'Unknown';
            const milesTravelled = ((totalSmartmastsDistanceTraveled[vesselId] || 0) / 1609.34).toFixed(2); // meters to miles
            const hoursOnline = ((totalSensorsOnlineDuration[vesselId] || 0) / (1000 * 60 * 60)).toFixed(2); // ms to hours
            const vesselsDetected = totalVesselsDetectedbySensors[vesselId] || 0;
            const row = [
                vesselId,
                unitId,
                name,
                regionGroupName,
                milesTravelled,
                hoursOnline,
                vesselsDetected,
            ];
            // Add super-category counts (per vessel)
            const superCatCounts = superCategories.map(c => (perVesselSuperCategorized[vesselId] && perVesselSuperCategorized[vesselId][c] || 0));
            row.push(...superCatCounts);
            // Add sub-category counts (per vessel)
            const subCatCounts = subCategories.map(c => (perVesselSubCategorized[vesselId] && perVesselSubCategorized[vesselId][c] || 0));
            row.push(...subCatCounts);
            perVesselRows.push(row);
            // Accumulate totals
            totalMiles += parseFloat(milesTravelled);
            totalHours += parseFloat(hoursOnline);
            totalDetected += vesselsDetected;
            for (let i = 0; i < superCategories.length; ++i) totalSuperCats[i] += superCatCounts[i];
            for (let i = 0; i < subCategories.length; ++i) totalSubCats[i] += subCatCounts[i];

            // Accumulate region group totals
            if (!regionGroupTotals[regionGroupName]) {
                regionGroupTotals[regionGroupName] = { miles: 0, hours: 0, detected: 0 };
                regionGroupSuperCats[regionGroupName] = superCategories.map(() => 0);
                regionGroupSubCats[regionGroupName] = subCategories.map(() => 0);
            }
            regionGroupTotals[regionGroupName].miles += parseFloat(milesTravelled);
            regionGroupTotals[regionGroupName].hours += parseFloat(hoursOnline);
            regionGroupTotals[regionGroupName].detected += vesselsDetected;
            for (let i = 0; i < superCategories.length; ++i) regionGroupSuperCats[regionGroupName][i] += superCatCounts[i];
            for (let i = 0; i < subCategories.length; ++i) regionGroupSubCats[regionGroupName][i] += subCatCounts[i];
        }
        // Write per-vessel rows
        for (const row of perVesselRows) worksheet.addRow(row);
        // Add a 'Total' row at the end of the per-vessel section
        const totalRow = [
            'Total', '', '', '',
            totalMiles.toFixed(2),
            totalHours.toFixed(2),
            totalDetected,
            ...totalSuperCats,
            ...totalSubCats
        ];
        const totalRowObj = worksheet.addRow(totalRow);
        totalRowObj.font = { bold: true };

        // Add a blank row between sections
        worksheet.addRow([]);

        // Write the summary section (all sensors, ever) with region group columns
        const regionGroupNamesList = Object.keys(regionGroupTotals).sort();

        // Header row for All Sensors Summary
        const allSensorsSummaryHeaderRow = ['All Sensors Summary', 'Total', ...regionGroupNamesList];
        const allSensorsSummaryRow = worksheet.addRow(allSensorsSummaryHeaderRow);
        allSensorsSummaryRow.font = { bold: true };

        // Miles row
        const milesRow = ['How many miles have SmartMast travelled?', totalMiles.toFixed(2)];
        regionGroupNamesList.forEach(regionName => {
            milesRow.push(regionGroupTotals[regionName].miles.toFixed(2));
        });
        worksheet.addRow(milesRow);

        // Hours row
        const hoursRow = ['How many hours have they been online?', totalHours.toFixed(2)];
        regionGroupNamesList.forEach(regionName => {
            hoursRow.push(regionGroupTotals[regionName].hours.toFixed(2));
        });
        worksheet.addRow(hoursRow);

        // Detected row
        const detectedRow = ['How many vessels detected?', totalDetected];
        regionGroupNamesList.forEach(regionName => {
            detectedRow.push(regionGroupTotals[regionName].detected);
        });
        worksheet.addRow(detectedRow);
        worksheet.addRow([]); // Add empty line before 'Total Detections by Super-Category'

        // Header row for Super-Category section
        const superCatHeaderRowData = ['Total Detections by Super-Category', 'Total', ...regionGroupNamesList];
        const superCatHeaderRow = worksheet.addRow(superCatHeaderRowData);
        superCatHeaderRow.font = { bold: true };

        // Super-category rows with region group columns
        for (let i = 0; i < superCategories.length; ++i) {
            const superCatRow = [superCategories[i], totalSuperCats[i]];
            regionGroupNamesList.forEach(regionName => {
                superCatRow.push(regionGroupSuperCats[regionName] ? regionGroupSuperCats[regionName][i] : 0);
            });
            worksheet.addRow(superCatRow);
        }
        worksheet.addRow([]); // Add empty line before 'Total Detections by Category'

        // Header row for Category section
        const subCatHeaderRowData = ['Total Detections by Category', 'Total', ...regionGroupNamesList];
        const subCatHeaderRow = worksheet.addRow(subCatHeaderRowData);
        subCatHeaderRow.font = { bold: true };

        // Sub-category rows with region group columns
        for (let i = 0; i < subCategories.length; ++i) {
            const subCatRow = [subCategories[i], totalSubCats[i]];
            regionGroupNamesList.forEach(regionName => {
                subCatRow.push(regionGroupSubCats[regionName] ? regionGroupSubCats[regionName][i] : 0);
            });
            worksheet.addRow(subCatRow);
        }

        // Merge cells for Super Category and Category in the per-vessel section header row only
        // Note: Header rows are now at rows 3 and 4 (due to date range header and empty row)
        const mainHeadersLen = mainHeaders.length;
        const headerRow1Index = 3;
        const headerRow2Index = 4;

        if (superCategories.length > 0) {
            worksheet.mergeCells(headerRow1Index, mainHeadersLen + 1, headerRow1Index, mainHeadersLen + superCategories.length);
        }
        if (subCategories.length > 0) {
            worksheet.mergeCells(headerRow1Index, mainHeadersLen + superCategories.length + 1, headerRow1Index, mainHeadersLen + superCategories.length + subCategories.length);
        }
        // Merge main header cells (single columns) for the per-vessel section
        for (let i = 1; i <= mainHeadersLen; ++i) {
            worksheet.mergeCells(headerRow1Index, i, headerRow2Index, i);
        }

        // Style header rows for the per-vessel section
        worksheet.getRow(headerRow1Index).font = { bold: true };
        worksheet.getRow(headerRow2Index).font = { bold: true };
        worksheet.getRow(headerRow1Index).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheet.getRow(headerRow2Index).alignment = { vertical: 'middle', horizontal: 'center' };

        // Autosize columns
        worksheet.columns.forEach(column => {
            let maxLength = 10;
            column.eachCell({ includeEmpty: true }, cell => {
                maxLength = Math.max(maxLength, (cell.value ? String(cell.value).length : 0));
            });
            column.width = maxLength + 2;
        });

        // Center align all numbered values (all columns except first four for per-vessel, total, and summary rows)
        // Per-vessel section: header is now at rows 3 and 4, data starts at row 5
        const perVesselStartRow = 5;
        const perVesselEndRow = perVesselStartRow + perVesselRows.length; // exclusive
        for (let i = perVesselStartRow; i < perVesselEndRow; ++i) {
            const row = worksheet.getRow(i);
            for (let j = 5; j <= row.cellCount; ++j) {
                row.getCell(j).alignment = { horizontal: 'center' };
            }
        }
        // 'Total' row (immediately after per-vessel rows)
        const totalRowIdx = perVesselEndRow;
        const totalRowObj2 = worksheet.getRow(totalRowIdx);
        for (let j = 5; j <= totalRowObj2.cellCount; ++j) {
            totalRowObj2.getCell(j).alignment = { horizontal: 'center' };
        }
        // Center align all value columns in summary/statistics section (all rows after totalRowIdx)
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > totalRowIdx) {
                if (row.cellCount >= 2) {
                    for (let j = 2; j <= row.cellCount; ++j) {
                        row.getCell(j).alignment = { horizontal: 'center' };
                    }
                }
            }
        });

        // Parse --output-dir flag
        let saveDir = process.cwd();
        const outputDirFlag = process.argv.find(arg => arg.startsWith('--output-dir='));
        if (outputDirFlag) {
            const flagValue = outputDirFlag.split('=')[1];
            if (!flagValue || !fs.existsSync(flagValue) || !fs.statSync(flagValue).isDirectory()) {
                console.error('Error: The specified directory in --output-dir does not exist. Exiting.');
                process.exit(1);
            }
            saveDir = flagValue;
        }
        // Generate timestamp for filename
        const now = new Date();
        const pad = n => n.toString().padStart(2, '0');
        const timestamp = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}_${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
        const excelPath = path.join(saveDir, `all_time_stats_${timestamp}.xlsx`);
        await workbook.xlsx.writeFile(excelPath);
        writeLine(`✅ All-time stats Excel file written to ${excelPath}`);
        process.exit(0);
    } catch (err) {
        console.error('Failed to generate all-time stats CSV:', err);
        process.exit(1);
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
        db.qmai.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(() => {
    generateAllTimeStatsCSV()
        .then(() => {
            writeLine('All-time stats script completed successfully');
            process.exit(0);
        })
        .catch(err => {
            console.error('All-time stats script failed:', err);
            process.exit(1);
        });
});