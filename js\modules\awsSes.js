const { SESClient, SendEmailCommand } = require('@aws-sdk/client-ses');

const SES_CONFIG = {
    region: "us-east-2",
    fromEmail: "<EMAIL>"
}
let emailCount = { date: new Date().toDateString(), count: 0 };
const DAILY_LIMIT = 2000;
const userHourlyCounts = new Map();
const ARTIFACT_ALERT_HOURLY_LIMIT = 5;
const EMAIL_TYPE = {
    ALERT: 1,
    SUMMARY: 2,
};
const BYPASS_HOURLY_LIMIT = {
    [EMAIL_TYPE.SUMMARY]: true
};

const sesClient = new SESClient({
    region: SES_CONFIG.region,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
});

function checkAndReserveDailyLimit() {
    const today = new Date().toDateString();
    if (emailCount.date !== today) {
        emailCount = { date: today, count: 0 };
    }

    if (emailCount.count >= DAILY_LIMIT) {
        return { allowed: false, currentCount: emailCount.count };
    }

    emailCount.count++;
    return { allowed: true, currentCount: emailCount.count };
}

function checkAndReserveUserHourlyLimit(email) {
    const now = new Date();
    const currentHour = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}-${String(now.getHours()).padStart(2, '0')}`;

    const userCount = userHourlyCounts.get(email);

    if (!userCount || userCount.hour !== currentHour) {
        userHourlyCounts.set(email, { hour: currentHour, count: 1 });
        return { allowed: true, currentCount: 1 };
    }

    if (userCount.count >= ARTIFACT_ALERT_HOURLY_LIMIT) {
        return { allowed: false, currentCount: userCount.count };
    }

    userCount.count++;
    return { allowed: true, currentCount: userCount.count };
}

async function sendEmail(to, subject, html, options = {}) {
    const { emailType = EMAIL_TYPE.ALERT } = options;
    const bypassesHourlyLimit = BYPASS_HOURLY_LIMIT[emailType] === true;

    const dailyLimitCheck = checkAndReserveDailyLimit();
    if (!dailyLimitCheck.allowed) {
        console.warn(`[AWS SES] ⚠️  GLOBAL DAILY LIMIT REACHED (${DAILY_LIMIT}/day, current: ${dailyLimitCheck.currentCount}). Email to ${to} NOT sent.`);
        return { success: false, reason: 'global_daily_limit' };
    }

    if (!bypassesHourlyLimit) {
        const userLimitCheck = checkAndReserveUserHourlyLimit(to);
        if (!userLimitCheck.allowed) {
            emailCount.count--;
            console.warn(`[AWS SES] ⚠️  USER HOURLY LIMIT REACHED (${ARTIFACT_ALERT_HOURLY_LIMIT}/hour, current: ${userLimitCheck.currentCount}) for ${to}. Email NOT sent.`);
            return { success: false, reason: 'user_hourly_limit' };
        }
    }

    const source = `"${process.env.NODE_ENV === 'local' ? 'TEST ' : ''}Quartermaster Alerts" <${SES_CONFIG.fromEmail}>`;

    const command = new SendEmailCommand({
        Source: source,
        Destination: {
            ToAddresses: [to]
        },
        Message: {
            Subject: {
                Data: subject,
                Charset: 'UTF-8'
            },
            Body: {
                Html: {
                    Data: html,
                    Charset: 'UTF-8'
                }
            }
        }
    });

    try {
        const result = await sesClient.send(command);
        console.log(`[AWS SES] Email sent successfully to ${to}, MessageId: ${result.MessageId}, Type: ${emailType}`);
        return { success: true, MessageId: result.MessageId };
    } catch (error) {
        emailCount.count--;
        if (!bypassesHourlyLimit) {
            const now = new Date();
            const currentHour = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}-${String(now.getHours()).padStart(2, '0')}`;
            const userCount = userHourlyCounts.get(to);
            if (userCount && userCount.hour === currentHour && userCount.count > 0) {
                userCount.count--;
            }
        }
        console.error(`[AWS SES] Failed to send email to ${to}:`, error);
        throw error;
    }
}

module.exports = {
    sendEmail,
    EMAIL_TYPE
};
